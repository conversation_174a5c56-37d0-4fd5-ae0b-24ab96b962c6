import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/ws_message_manager.dart';
import 'package:rolio/manager/ws_connection_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';
import 'package:rolio/modules/chat/service/message_service.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/painting.dart';

/// 缓存清理服务
/// 
/// 负责在用户登出时清理所有缓存数据，以及响应清理事件
class CacheCleanupService extends GetxService {
  /// 单例实例
  static CacheCleanupService? _instance;

  /// 缓存管理器
  final CacheManager _cacheManager = CacheManager.to;

  /// WebSocket消息管理器
  final WsMessageManager _wsMessageManager = WsMessageManager();

  /// WebSocket连接管理器
  final WsConnectionManager _wsConnectionManager = WsConnectionManager();
  
  /// Worker管理
  final List<Worker> _workers = [];

  /// 私有构造函数
  CacheCleanupService._() {
    _setupEventListeners();
  }

  /// 获取单例实例
  static CacheCleanupService getInstance() {
    _instance ??= CacheCleanupService._();
    return _instance!;
  }

  /// 注册为GetX服务（优化版本）
  static Future<void> init() async {
    final instance = getInstance();

    // 注册为GetX服务
    if (!Get.isRegistered<CacheCleanupService>()) {
      Get.put<CacheCleanupService>(instance, permanent: true);
    }

    // 延迟执行缓存清理，不阻塞启动
    // 不再在这里同步执行cleanupCachesOnRestart
    
    LogUtil.debug('CacheCleanupService 初始化完成');
    return;
  }

  /// 从GetX获取单例
  static CacheCleanupService get to => Get.find<CacheCleanupService>();
  
  /// 设置事件监听器
  void _setupEventListeners() {
    LogUtil.debug('CacheCleanupService - 设置事件监听器');

    // 监听角色资源清理事件
    _workers.add(ever(GlobalEventState.to.roleResourceCleanup, (Map<String, dynamic>? data) {
      if (data != null) {
        final roleId = data['roleId'] as int?;
        final conversationId = data['conversationId'] as int?;
        final reason = data['reason'] as String?;

        if (roleId != null && roleId > 0) {
          LogUtil.debug('收到角色资源清理事件: roleId=$roleId, 原因: $reason');
          _cleanupRoleResources(roleId, conversationId: conversationId);
        }
      }
    }));

    // 监听会话资源清理事件
    _workers.add(ever(GlobalEventState.to.conversationResourceCleanup, (Map<String, dynamic>? data) {
      if (data != null) {
        final conversationId = data['conversationId'] as int?;
        final reason = data['reason'] as String?;

        if (conversationId != null && conversationId > 0) {
          LogUtil.debug('收到会话资源清理事件: conversationId=$conversationId, 原因: $reason');
          _cleanupConversationResources(conversationId);
        }
      }
    }));

    // 监听会话缓存清理事件
    _workers.add(ever(GlobalEventState.to.sessionCacheCleanup, (Map<String, dynamic>? data) {
      if (data != null) {
        final conversationId = data['conversationId'] as int?;
        final reason = data['reason'] as String?;

        if (conversationId != null && conversationId > 0) {
          LogUtil.debug('收到会话缓存清理事件: conversationId=$conversationId, 原因: $reason');
          _cleanupSessionCache(conversationId);
        }
      }
    }));

    // 监听角色绑定清理事件
    _workers.add(ever(GlobalEventState.to.roleBindingCleanup, (Map<String, dynamic>? data) {
      if (data != null) {
        final roleId = data['roleId'] as int?;
        final conversationId = data['conversationId'] as int?;

        if (roleId != null && roleId > 0) {
          LogUtil.debug('收到角色绑定清理事件: roleId=$roleId, conversationId=$conversationId');
          _cleanupRoleBinding(roleId, conversationId);
        }
      }
    }));

    LogUtil.debug('CacheCleanupService - 事件监听器设置完成');
  }
  
  /// 清理角色相关资源
  void _cleanupRoleResources(int roleId, {int? conversationId}) {
    try {
      LogUtil.info('开始清理角色$roleId的资源');
      
      // 取消角色的WebSocket订阅
      if (Get.isRegistered<AiChannelManager>()) {
        final channelManager = Get.find<AiChannelManager>();
        // 取消该角色的WebSocket订阅
        channelManager.unsubscribeFromRole(roleId);
        LogUtil.debug('已取消角色$roleId的WebSocket订阅');
      }
      
      // 清除MessageTracker中的角色订阅和待处理消息
      try {
        final messageTracker = MessageTracker();
        // 先清理特定角色的待处理消息
        int removedCount = messageTracker.clearPendingMessagesByRoleId(roleId);
        if (removedCount > 0) {
          LogUtil.debug('已清除角色$roleId的$removedCount条待处理消息');
        }
        // 再移除角色订阅
        messageTracker.removeRoleSubscription(roleId);
        LogUtil.debug('已清除MessageTracker中角色$roleId的订阅');
      } catch (e) {
        LogUtil.error('清除MessageTracker中角色$roleId的数据失败: $e');
      }
      
      // 清除WsMessageManager中的角色订阅
      try {
        if (Get.isRegistered<WsMessageManager>()) {
          final wsMessageManager = Get.find<WsMessageManager>();
          wsMessageManager.clearChannelHandlers(roleId);
          LogUtil.debug('已清除WsMessageManager中角色$roleId的订阅');
        }
      } catch (e) {
        LogUtil.error('清除WsMessageManager中角色$roleId的订阅失败: $e');
      }
      
      // 如果是当前活跃角色，清除其在ChatManager中的状态
      try {
        if (Get.isRegistered<ChatManager>()) {
          final chatManager = Get.find<ChatManager>();
          if (chatManager.activeAiRoleId.value == roleId) {
            LogUtil.debug('被清理的角色是当前活跃角色，重置ChatManager中的状态');
            // 重置会话ID，避免使用已删除的会话
            chatManager.setActiveSession(0, roleId);
          }
        }
      } catch (e) {
        LogUtil.error('重置ChatManager中角色状态失败: $e');
      }
      
      LogUtil.info('角色$roleId的资源清理完成');
    } catch (e) {
      LogUtil.error('清理角色$roleId资源失败: $e');
    }
  }
  
  /// 清理会话相关资源
  Future<void> _cleanupConversationResources(int conversationId) async {
    try {
      LogUtil.info('开始清理会话$conversationId的资源');
      
      // 查找与该会话关联的所有角色
      if (Get.isRegistered<AiChannelManager>()) {
        final channelManager = Get.find<AiChannelManager>();
        // 获取当前所有订阅角色
        final subscribedRoles = channelManager.getSubscribedRoleIds();
        LogUtil.debug('发现已订阅角色: $subscribedRoles');
        
        // 查找任何与该会话可能相关的角色
        final relatedRoles = <int>[];
        
        // 遍历所有已订阅角色，检查它们的会话ID
        for (final roleId in subscribedRoles) {
          final roleConversationId = channelManager.getConversationIdForRole(roleId);
          if (roleConversationId == conversationId) {
            relatedRoles.add(roleId);
          }
        }
        
        if (relatedRoles.isNotEmpty) {
          LogUtil.debug('发现与会话$conversationId相关的角色: $relatedRoles');
          
          // 清理这些相关角色的绑定
          for (final roleId in relatedRoles) {
            // 清理角色资源
            _cleanupRoleResources(roleId, conversationId: conversationId);
            
            // 清理角色绑定
            _cleanupRoleBinding(roleId, conversationId);
          }
        } else {
          LogUtil.debug('未找到与会话$conversationId相关的角色');
        }
      }
      
      // 清理会话缓存
      await _cleanupSessionCache(conversationId);
      
      LogUtil.info('会话$conversationId的资源清理完成');
    } catch (e) {
      LogUtil.error('清理会话$conversationId资源失败: $e');
    }
  }
  
  /// 清理角色绑定关系
  Future<void> _cleanupRoleBinding(int roleId, int? conversationId) async {
    try {
      LogUtil.info('清理角色$roleId的绑定关系');

      // 延迟清理，避免在关键操作期间执行
      await Future.delayed(const Duration(milliseconds: 200));

      // 清除SessionBindingService中的绑定
      if (Get.isRegistered<SessionBindingService>()) {
        final bindingService = Get.find<SessionBindingService>();
        bindingService.clearBinding(roleId);
        LogUtil.debug('已清理角色绑定关系: roleId=$roleId, conversationId=$conversationId');

        // 更新角色对象的会话ID为0（表示新会话）
        if (Get.isRegistered<RoleProvider>()) {
          final roleService = Get.find<RoleProvider>();
          await roleService.updateRoleConversationId(roleId, 0);
          LogUtil.debug('已将角色会话ID重置为0: roleId=$roleId');
        }
      }
    } catch (e) {
      LogUtil.error('清理角色$roleId绑定关系失败: $e');
    }
  }
  
  /// 清理会话缓存
  Future<void> _cleanupSessionCache(int conversationId) async {
    try {
      LogUtil.info('清除会话$conversationId的所有相关缓存');
      
      // 清除聊天历史缓存
      final cacheKey = 'chat_history_$conversationId';
      await _cacheManager.remove(cacheKey);
      LogUtil.debug('已清除会话聊天历史缓存: $cacheKey');
      
      // 清除可能存在的其他相关缓存
      final relatedKeys = [
        'session_$conversationId',
        'conversation_$conversationId',
        'messages_$conversationId',
      ];
      
      for (final key in relatedKeys) {
        await _cacheManager.remove(key);
      }
      
      // 清除MessageService中的会话状态
      if (Get.isRegistered<MessageService>()) {
        try {
          final messageService = Get.find<MessageService>();
          messageService.clearConversationState(conversationId);
          LogUtil.debug('已清除MessageService中会话$conversationId的状态');
        } catch (e) {
          LogUtil.error('清除MessageService中会话$conversationId的状态失败: $e');
        }
      }
      
      // 清除WsManager中的会话通道
      if (Get.isRegistered<WsManager>()) {
        try {
          final wsManager = Get.find<WsManager>();
          wsManager.switchToGlobalChannel();
          LogUtil.debug('已将WsManager切换回全局通道');
        } catch (e) {
          LogUtil.error('切换WsManager通道失败: $e');
        }
      }
      
      LogUtil.info('会话$conversationId的所有相关缓存已清除');
    } catch (e) {
      LogUtil.error('清除会话$conversationId缓存失败: $e');
    }
  }

  /// 清理所有缓存
  /// 
  /// 在用户登出时调用，清理所有缓存数据
  Future<bool> cleanupAllCaches() async {
    LogUtil.info('开始清理所有缓存数据...');
    // 直接调用彻底清理方法，确保所有缓存都被清除
    return await clearAllCachesCompletely();
  }
  
  /// 清理未使用的资源，减轻内存压力
  /// 不会关闭WebSocket连接
  Future<bool> cleanupUnusedResources() async {
    LogUtil.info('开始清理未使用资源...');
    bool success = true;
    
    try {
      // 1. 清理内存缓存
      try {
        final result = await _cacheManager.clear(strategy: CacheStrategy.memoryOnly);
        if (!result) {
          LogUtil.warn('内存缓存清理可能不完整');
          success = false;
        } else {
          LogUtil.debug('内存缓存已清空');
        }
      } catch (e) {
        LogUtil.error('清理内存缓存失败: $e');
        success = false;
      }
      
      // 2. 清理图片缓存
      try {
        // 在UI线程上执行图片缓存清理
        await Future.microtask(() {
          final imageCache = PaintingBinding.instance.imageCache;
          // 只清理不活跃的图片
          imageCache.clearLiveImages();
          LogUtil.debug('图片缓存已清理');
        });
      } catch (e) {
        LogUtil.error('清理图片缓存失败: $e');
        success = false;
      }
      
      // 3. 其他资源清理，但不影响当前操作
      try {
        // 触发一次垃圾回收
        Future.delayed(const Duration(milliseconds: 200), () {
          if (kDebugMode) {
            // ignore: avoid_print
            print('请求释放内存');
          }
        });
      } catch (e) {
        // 忽略此处的错误
      }
      
      LogUtil.info('未使用资源清理${success ? '完成' : '部分失败'}');
      return success;
    } catch (e) {
      LogUtil.error('清理未使用资源出错: $e');
      return false;
    }
  }
  
  /// 清理所有Workers
  void _disposeAllWorkers() {
    for (var worker in _workers) {
      worker.dispose();
    }
    _workers.clear();
  }
  
  @override
  void onClose() {
    // 清理所有Workers
    _disposeAllWorkers();
    super.onClose();
  }

  /// 完全清空所有缓存
  /// 
  /// 不区分缓存类型，直接清空所有缓存数据
  /// 
  /// [closeWebSocket] - 是否关闭WebSocket连接，默认为true，应用刚启动时应为false
  Future<bool> clearAllCachesCompletely({bool closeWebSocket = true}) async {
    LogUtil.info('开始完全清空所有缓存数据...');
    bool success = true;
    
    try {
      // 清理SharedPreferences中的所有数据
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();
        LogUtil.debug("SharedPreferences数据已全部清除!");
      } catch (e) {
        LogUtil.error('清理SharedPreferences数据失败: $e');
        success = false;
      }
      
      // 清理SessionBindingService中的所有角色会话绑定
      try {
        if (Get.isRegistered<SessionBindingService>()) {
          final sessionBindingService = Get.find<SessionBindingService>();
          await sessionBindingService.clearAllBindings();
          LogUtil.debug('已清除所有角色会话绑定');
        }
      } catch (e) {
        LogUtil.error('清除角色会话绑定失败: $e');
        success = false;
      }
      
      // 清理消息跟踪器
      try {
        // 记录清理前的待处理消息数量
        final messageTracker = MessageTracker();
        final pendingCount = messageTracker.getPendingMessageCount();
        if (pendingCount > 0) {
          LogUtil.debug('清理前，待处理消息数量: $pendingCount');
          messageTracker.logAllPendingMessages();
        }
        
        // 清空所有待处理消息和角色订阅
        messageTracker.clearAll();
        LogUtil.debug('已清理消息跟踪器的所有待处理消息和角色订阅');
      } catch (e) {
        LogUtil.error('清理消息跟踪器失败: $e');
        success = false;
      }
      
      // 清理WsMessageManager的处理器和订阅
      try {
        _wsMessageManager.clearAllHandlersAndSubscriptions();
        LogUtil.debug('已清理WsMessageManager的所有处理器和订阅');
      } catch (e) {
        LogUtil.error('清理WsMessageManager失败: $e');
        success = false;
      }
      
      // 清理MessageService中的所有缓存，包括待处理消息
      try {
        if (Get.isRegistered<MessageService>()) {
          final messageService = Get.find<MessageService>();
          messageService.clearAllMessageCache();
          LogUtil.debug('已清空MessageService所有缓存和待处理消息');
        }
      } catch (e) {
        LogUtil.error('清空MessageService缓存失败: $e');
        success = false;
      }
      
      // 清理AiChannelManager的缓存
      try {
        if (Get.isRegistered<AiChannelManager>()) {
          final aiChannelManager = Get.find<AiChannelManager>();
          
          // 先获取并记录当前订阅的角色
          final subscribedRoles = aiChannelManager.getSubscribedRoleIds();
          if(subscribedRoles.isNotEmpty) {
            LogUtil.debug('清理AiChannelManager前的角色订阅: $subscribedRoles');
          }
          
          // 清理所有缓存和订阅
          aiChannelManager.clearAllCache();
          LogUtil.debug('已清空AiChannelManager缓存和角色订阅');
        }
      } catch (e) {
        LogUtil.error('清空AiChannelManager缓存失败: $e');
        success = false;
      }
      
      // 清理GlobalState中的AI角色状态
      try {
        if (Get.isRegistered<GlobalState>()) {
          final globalState = Get.find<GlobalState>();
          
          // 记录清理前的状态
          final currentAiRole = globalState.currentAiRole.value;
          final roleId = currentAiRole?.id ?? 0;
          final isGenerating = globalState.isAiGenerating.value;
          
          // 清理当前AI角色状态
          globalState.clearCurrentAiRole();
          // 重置AI生成状态
          globalState.setAiGeneratingState(false);
          
          LogUtil.debug('已清空GlobalState中的AI角色状态，之前角色ID: $roleId, 生成状态: $isGenerating');
        }
      } catch (e) {
        LogUtil.error('清空GlobalState中的AI角色状态失败: $e');
        success = false;
      }
      
      // 直接清空所有内存和持久化缓存
      final clearResult = await _cacheManager.clear(strategy: CacheStrategy.both);
      if (!clearResult) {
        LogUtil.warn('缓存清空可能不完整');
        success = false;
      } else {
        LogUtil.debug('所有缓存已清空');
      }
      
      // 清理文件缓存目录
      try {
        final cacheDir = await getApplicationCacheDirectory();
        if (cacheDir.existsSync()) {
          // 尝试删除并重建缓存目录以确保完全清空
          try {
            await cacheDir.delete(recursive: true);
            await cacheDir.create();
            LogUtil.debug('缓存目录已重建');
          } catch (e) {
            // 如果无法删除整个目录，则尝试删除所有文件
            final entities = cacheDir.listSync(recursive: true);
            for (var entity in entities) {
              if (entity is File) {
                try {
                  await entity.delete();
                } catch (e) {
                  LogUtil.warn('删除缓存文件失败: ${entity.path}, 错误: $e');
                  success = false;
                }
              }
            }
            LogUtil.debug('缓存文件清理完成');
          }
        }
      } catch (e) {
        LogUtil.error('清理文件缓存目录失败: $e');
        success = false;
      }
      
      // 关闭WebSocket连接（如果需要）
      if (closeWebSocket) {
        try {
          await _wsConnectionManager.disconnect();
          LogUtil.debug('WebSocket连接已关闭');
        } catch (e) {
          LogUtil.error('关闭WebSocket连接失败: $e');
          success = false;
        }
      } else {
        LogUtil.debug('保留WebSocket连接（应用刚启动或重启）');
      }
      
      LogUtil.info('缓存完全清空${success ? '成功' : '部分失败'}');
      return success;
    } catch (e) {
      LogUtil.error('缓存完全清空过程中发生错误: $e');
      return false;
    }
  }

  /// 应用重启时清理缓存
  /// 
  /// 在应用重启时调用，清理所有聊天记录和会话相关缓存
  Future<bool> cleanupCachesOnRestart() async {
    LogUtil.info('应用重启，开始清理缓存数据...');
    // 应用重启时不关闭WebSocket连接
    return await clearAllCachesCompletely(closeWebSocket: false);
  }
}