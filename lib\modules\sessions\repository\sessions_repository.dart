import 'package:rolio/common/interfaces/base_repository.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/modules/sessions/model/session.dart';
import 'package:rolio/env.dart';
import 'package:rolio/common/constants/http_url_constants.dart';
import 'package:get/get.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/models/page_data.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/cache_constants.dart';

/// 会话仓库
/// 
/// 负责处理会话相关的数据访问逻辑
class SessionsRepository extends BaseRepository {
  // API路径
  final String _apiPath = HttpUrl.conversationPath;

  
  // 缓存键前缀
  static const String _sessionListCacheKey = 'sessions_list';
  static const String _sessionDetailCachePrefix = 'session_detail_';
  
  // 缓存管理器
  final CacheManager _cacheManager = CacheManager.to;
  
  // 简化缓存策略：统一使用CacheConstants
  int get _cacheExpiryMs => CacheConstants.sessionListExpiryMs;
  int get _detailCacheExpiryMs => CacheConstants.sessionDetailExpiryMs;
  
  // 从缓存配置类中获取缓存策略（新增，不替换原有代码）
  CacheStrategy get _getStrategy => CacheConstants.defaultGetStrategy;
  CacheStrategy get _setStrategy => CacheConstants.defaultCacheStrategy;
  
  /// 获取全局状态实例
  GlobalState? _globalState;
  GlobalState get globalState {
    _globalState ??= Get.find<GlobalState>();
    return _globalState!;
  }
  
  /// 构造函数
  SessionsRepository();
  
  /// 获取API基础URL
  String get _baseUrl => Env.envConfig.aiServiceUrl;
  
  /// 确保URL格式正确
  String _ensureUrlFormat(String baseUrl, String path) {
    // 确保baseUrl不以斜杠结尾
    String normalizedBaseUrl = baseUrl;
    if (normalizedBaseUrl.endsWith('/')) {
      normalizedBaseUrl = normalizedBaseUrl.substring(0, normalizedBaseUrl.length - 1);
    }
    
    // 确保path以斜杠开头
    String normalizedPath = path;
    if (!normalizedPath.startsWith('/')) {
      normalizedPath = '/$normalizedPath';
    }
    
    final finalUrl = '$normalizedBaseUrl$normalizedPath';
    LogUtil.debug('URL构建: baseUrl=$baseUrl, path=$path, finalUrl=$finalUrl');
    return finalUrl;
  }
  
  /// 生成缓存键
  String _generateCacheKey(String prefix, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return prefix;
    }

    // 按键排序，确保相同参数生成相同的缓存键
    final sortedKeys = params.keys.toList()..sort();
    final parts = sortedKeys.map((key) {
      final value = params[key];
      // 确保值的字符串表示一致性
      final valueStr = value?.toString() ?? 'null';
      return '$key=$valueStr';
    }).join('_');

    return '${prefix}_$parts';
  }

  /// 验证缓存数据完整性
  bool _validateCacheData(Map<String, dynamic> cached) {
    try {
      // 检查必需字段
      if (cached['items'] is! List) {
        LogUtil.warn('缓存数据验证失败: items字段不是List类型');
        return false;
      }

      if (cached['page'] is! int) {
        LogUtil.warn('缓存数据验证失败: page字段不是int类型');
        return false;
      }

      if (cached['size'] is! int) {
        LogUtil.warn('缓存数据验证失败: size字段不是int类型');
        return false;
      }

      if (cached['total'] is! int) {
        LogUtil.warn('缓存数据验证失败: total字段不是int类型');
        return false;
      }

      // 检查items中的数据格式
      final items = cached['items'] as List;
      for (final item in items) {
        if (item is! Map<String, dynamic>) {
          LogUtil.warn('缓存数据验证失败: items中包含非Map类型数据');
          return false;
        }

        // 检查Session必需字段
        final sessionMap = item as Map<String, dynamic>;
        if (!sessionMap.containsKey('id') || sessionMap['id'] is! int) {
          LogUtil.warn('缓存数据验证失败: Session缺少有效的id字段');
          return false;
        }
      }

      return true;
    } catch (e) {
      LogUtil.error('缓存数据验证异常: $e');
      return false;
    }
  }
  
  /// 获取会话分页列表（使用PageData和PageRequest）
  Future<PageData<Session>> getPagedList(PageRequest request, {bool forceRefresh = false}) async {
    try {
      // 使用请求参数生成缓存键，而不是使用hashCode
      final cacheKey = _generateCacheKey(_sessionListCacheKey, request.toParams());
      LogUtil.debug('生成会话列表缓存键: $cacheKey');
      
      // 只有在非强制刷新时才从缓存获取
      if (!forceRefresh) {
        LogUtil.debug('从缓存获取会话列表: $cacheKey');
        final cached = await _cacheManager.get<Map<String, dynamic>>(
          cacheKey,
          strategy: CacheConstants.defaultGetStrategy,
          maxAge: CacheConstants.sessionListExpiryMs,
        );
        
        if (cached != null) {
          try {
            // 验证缓存数据完整性
            if (!_validateCacheData(cached)) {
              LogUtil.warn('会话列表缓存数据格式异常，清除缓存: $cacheKey');
              await _cacheManager.remove(cacheKey, strategy: CacheConstants.defaultCacheStrategy);
              // 缓存数据异常，跳过缓存直接从服务器获取
            } else {

            // 手动解析PageData
            final page = cached['page'] as int? ?? 1;
            final size = cached['size'] as int? ?? 10;
            final total = cached['total'] as int? ?? 0;
            // 解析Session列表
            final itemsJson = cached['items'] as List<dynamic>? ?? [];
            final items = itemsJson
                .map((item) => item is Map<String, dynamic> ? Session.fromMap(item) : null)
                .where((item) => item != null)
                .cast<Session>()
                .toList();

              LogUtil.debug('从缓存获取会话列表成功: $cacheKey, 条目数: ${items.length}');
              return PageData<Session>(
                page: page,
                size: size,
                total: total,
                items: items,
              );
            }
          } catch (e) {
            LogUtil.error('解析缓存的会话列表失败: $e，清除损坏的缓存');
            await _cacheManager.remove(cacheKey, strategy: CacheConstants.defaultCacheStrategy);
            // 缓存解析失败，继续从服务器获取
          }
        }
      } else {
        LogUtil.debug('强制刷新，跳过缓存获取会话列表');
      }
      
      final url = _ensureUrlFormat(_baseUrl, _apiPath);
      LogUtil.debug('获取会话列表，URL: $url, 请求参数: ${request.toParams()}');
      
      final response = await HttpManager.get(
        url: url,
        params: request.toParams(),
      );
      
      if (!response.isSuccess) {
        LogUtil.warn('获取会话列表失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return PageData.empty();
      }
      
      // 解析分页数据
      final pageData = _parsePagedResponse(response.rawData);
      
      // 写入缓存 - 使用手动序列化并验证数据完整性
      try {
        final serializedData = {
          'page': pageData.page,
          'size': pageData.size,
          'total': pageData.total,
          'totalPages': pageData.totalPages,
          'hasNext': pageData.hasNext,
          'hasPrevious': pageData.hasPrevious,
          'items': pageData.items.map((session) => session.toMap()).toList(),
          '_cached_at': DateTime.now().millisecondsSinceEpoch, // 添加缓存时间戳
        };

        // 验证序列化数据的完整性
        if (_validateCacheData(serializedData)) {
          await _cacheManager.set(
            cacheKey,
            serializedData,
            strategy: CacheConstants.defaultCacheStrategy,
            expiry: CacheConstants.sessionListExpiryMs,
          );
          LogUtil.debug('会话列表缓存写入成功: $cacheKey, 条目数: ${pageData.items.length}');
        } else {
          LogUtil.warn('会话列表数据格式异常，跳过缓存写入: $cacheKey');
        }
      } catch (e) {
        LogUtil.error('持久化缓存设置失败: $cacheKey, 错误: $e');
      }
      
      return pageData;
    } catch (e) {
      LogUtil.error('获取会话列表失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
      return PageData.empty();
    }
  }
  
  /// 解析分页响应
  PageData<Session> _parsePagedResponse(dynamic data) {
    try {
      LogUtil.debug('解析会话列表数据类型: ${data.runtimeType}');
      
      // 记录原始JSON结构的键，帮助调试
      if (data is Map<String, dynamic>) {
        LogUtil.debug('JSON包含的键: ${data.keys.toList()}');
      }
      
      // 处理标准后端格式: {code: 200, message: "...", data: {items: [...], ...}, ...}
      if (data is Map<String, dynamic> && data.containsKey('data')) {
        final dataObj = data['data'];
        if (dataObj is Map<String, dynamic> && dataObj.containsKey('items')) {
          // 提取分页信息
          final page = dataObj['page'] as int? ?? 1;
          final size = dataObj['size'] as int? ?? 10;
          final total = dataObj['total'] as int? ?? 0;
          
          // 获取项目列表
          final items = dataObj['items'] as List<dynamic>? ?? [];
          
          // 转换为Session对象
          final sessions = items
              .map((item) => item is Map<String, dynamic> ? Session.fromMap(item) : null)
              .where((session) => session != null)
              .cast<Session>()
              .toList();
          
          // 计算总页数
          final totalPages = (total / size).ceil();

          
          LogUtil.debug('成功解析${sessions.length}个会话，总数: $total, 页码: $page/$totalPages');
          
          // 移除客户端排序逻辑，使用服务端已经排好序的数据
          // 后端已经按照最后一条消息时间从新到旧排序
          
          return PageData<Session>(
            page: page,
            size: size,
            total: total,
            items: sessions,
          );
        }
      }
      
      LogUtil.warn('未知的会话数据格式');
      ErrorHandler.handleException(Exception('会话数据格式错误'), showSnackbar: false);
      return PageData.empty();
    } catch (e) {
      LogUtil.error('解析会话数据失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
      return PageData.empty();
    }
  }
  
  /// 根据ID获取会话
  Future<Session?> getById(int id) async {
    final cacheKey = '$_sessionDetailCachePrefix$id';
    
    // 优先从缓存获取
    final cached = await _cacheManager.get<Session>(
      cacheKey,
      strategy: CacheConstants.defaultGetStrategy,
      fromJson: Session.fromMap,
    );
    
    if (cached != null) {
      LogUtil.debug('从缓存获取会话，ID: $id');
      return cached;
    }
    
    try {
      final url = _ensureUrlFormat(_baseUrl, '$_apiPath$id');
      LogUtil.debug('获取会话，ID: $id，URL: $url');
      
      final response = await HttpManager.get(
        url: url,
      );
      
      if (!response.isSuccess) {
        LogUtil.warn('获取会话失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return null;
      }
      
      // 解析会话数据
      Session? session = _parseSessionResponse(response.rawData);
      
      if (session == null || session.id == 0) {
        LogUtil.warn('解析后会话无效');
        ErrorHandler.handleException(Exception('会话数据无效'), showSnackbar: false);
        return null;
      }
      
      LogUtil.debug('成功获取会话: ID=$id, 标题=${session.title}');
      
      // 写入缓存
      await _cacheManager.set(
        cacheKey,
        session,
        strategy: CacheConstants.defaultCacheStrategy,
        expiry: CacheConstants.sessionDetailExpiryMs,
      );
      
      return session;
    } catch (e) {
      LogUtil.error('获取会话失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
      return null;
    }
  }
  
  /// 解析单个会话响应
  Session? _parseSessionResponse(dynamic data) {
    try {
      LogUtil.debug('解析会话数据类型: ${data.runtimeType}');
      
      // 记录原始JSON结构的键，帮助调试
      if (data is Map<String, dynamic>) {
        LogUtil.debug('JSON包含的键: ${data.keys.toList()}');
      }
      
      Map<String, dynamic> sessionData = {};
      
      // 处理不同的数据格式
      if (data is Map<String, dynamic>) {
        // 直接是会话数据
        if (data.containsKey('id')) {
          sessionData = data;
        } 
        // 嵌套在data字段中
        else if (data.containsKey('data')) {
          final dynamic nestedData = data['data'];
          if (nestedData is Map<String, dynamic>) {
            sessionData = nestedData;
          }
        }
      }
      
      if (sessionData.isEmpty) {
        LogUtil.warn('会话数据格式不正确或为空: $data');
        ErrorHandler.handleException(Exception('会话数据格式错误'), showSnackbar: false);
        return null;
      }
      
      return Session.fromMap(sessionData);
    } catch (e) {
      LogUtil.error('解析会话失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
      return null;
    }
  }
  
  /// 清除会话列表缓存
  Future<void> _clearListCache() async {
    try {
      LogUtil.debug('清除会话列表缓存');
      
      // 查找所有会话列表相关的缓存键并清除
      final cacheKeys = await _cacheManager.getKeys();
      final sessionListCacheKeys = cacheKeys.where(
        (key) => key.startsWith(_sessionListCacheKey)
      ).toList();
      
      if (sessionListCacheKeys.isNotEmpty) {
        LogUtil.debug('找到 ${sessionListCacheKeys.length} 个会话列表缓存需要清除');
        for (final key in sessionListCacheKeys) {
          await _cacheManager.remove(key, strategy: CacheConstants.defaultCacheStrategy);
        }
      }
      
      LogUtil.debug('已清除会话列表缓存');
    } catch (e) {
      LogUtil.error('清除会话列表缓存失败: $e');
    }
  }
  
  /// 删除会话
  Future<bool> delete(int id) async {
    try {
      LogUtil.debug('删除会话: ID=$id');
      
      final url = _ensureUrlFormat(_baseUrl, '$_apiPath/$id');
      LogUtil.debug('删除会话，URL: $url');
      
      final response = await HttpManager.put(
        url: url,
      );
      
      
      if (!response.isSuccess) {
        LogUtil.warn('删除会话失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return false;
      }
      
      LogUtil.debug('成功删除会话: ID=$id');
      
      // 删除成功后清理相关缓存
      await _clearRelatedCache(id);
      
      return true;
    } catch (e) {
      LogUtil.error('删除会话失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e);
      return false;
    }
  }
  
  /// 置顶会话
  Future<bool> pinConversation(int id) async {
    try {
      LogUtil.debug('置顶会话: ID=$id');
      
      final url = _ensureUrlFormat(_baseUrl, '$_apiPath/$id/pin');
      LogUtil.debug('置顶会话，URL: $url');
      
      final response = await HttpManager.put(
        url: url,
      );
      
      if (!response.isSuccess) {
        LogUtil.warn('置顶会话失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return false;
      }
      
      LogUtil.debug('成功置顶会话: ID=$id');
      
      // 置顶成功后清理相关缓存
      await _clearListCache();
      
      return true;
    } catch (e) {
      LogUtil.error('置顶会话失败: $e');
      ErrorHandler.handleException(e);
      return false;
    }
  }
  
  /// 取消置顶会话
  Future<bool> unpinConversation(int id) async {
    try {
      LogUtil.debug('取消置顶会话: ID=$id');
      
      final url = _ensureUrlFormat(_baseUrl, '$_apiPath/$id/unpin');
      LogUtil.debug('取消置顶会话，URL: $url');
      
      final response = await HttpManager.put(
        url: url,
      );
      
      if (!response.isSuccess) {
        LogUtil.warn('取消置顶会话失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return false;
      }
      
      LogUtil.debug('成功取消置顶会话: ID=$id');
      
      // 取消置顶成功后清理相关缓存
      await _clearListCache();
      
      return true;
    } catch (e) {
      LogUtil.error('取消置顶会话失败: $e');
      ErrorHandler.handleException(e);
      return false;
    }
  }
  
  /// 隐藏会话
  Future<bool> hideConversation(int id) async {
    try {
      LogUtil.debug('隐藏会话: ID=$id');
      
      final url = _ensureUrlFormat(_baseUrl, '$_apiPath/$id/hide');
      LogUtil.debug('隐藏会话，URL: $url');
      
      final response = await HttpManager.put(
        url: url,
      );
      
      if (!response.isSuccess) {
        LogUtil.warn('隐藏会话失败: ${response.msg}');
        ErrorHandler.handleException(Exception('${response.msg}'), showSnackbar: false);
        return false;
      }
      
      LogUtil.debug('成功隐藏会话: ID=$id');
      
      // 隐藏成功后清理相关缓存
      await _clearListCache();
      
      return true;
    } catch (e) {
      LogUtil.error('隐藏会话失败: $e');
      ErrorHandler.handleException(e);
      return false;
    }
  }
  
  /// 清理相关缓存（删除会话后）
  Future<void> _clearRelatedCache(int id) async {
    try {
      // 删除会话详情缓存
      final detailCacheKey = '$_sessionDetailCachePrefix$id';
      await _cacheManager.remove(
        detailCacheKey,
        strategy: CacheConstants.defaultCacheStrategy,
      );
      
      // 更新会话列表缓存，而非清空
      await _updateSessionListCaches(id);
      
      LogUtil.debug('已清理会话相关缓存: ID=$id');
    } catch (e) {
      LogUtil.error('清理相关缓存失败: $e');
      // 内部方法，不向用户显示错误
    }
  }
  
  /// 更新会话列表缓存（增量更新）
  Future<void> _updateSessionListCaches(int deletedSessionId) async {
    try {
      LogUtil.debug('开始增量更新会话列表缓存，删除会话ID: $deletedSessionId');
      
      // 获取所有缓存键
      final cacheKeys = await _cacheManager.getKeys();
      
      // 筛选出会话列表的缓存键
      final sessionListCacheKeys = cacheKeys.where(
        (key) => key.startsWith(_sessionListCacheKey)
      ).toList();
      
      if (sessionListCacheKeys.isEmpty) {
        LogUtil.debug('未找到会话列表缓存，无需更新');
        return;
      }
      
      LogUtil.debug('找到 ${sessionListCacheKeys.length} 个会话列表缓存需要更新');
      
      // 缓存优化：按页码组织缓存，合并参数相似的缓存
      Map<int, Map<String, dynamic>> consolidatedCaches = {};
      Map<String, String> oldToNewKeyMap = {};
      
      // 处理每个会话列表缓存
      for (final cacheKey in sessionListCacheKeys) {
        // 获取当前缓存的会话列表
        final cached = await _cacheManager.get<Map<String, dynamic>>(
          cacheKey,
          strategy: CacheConstants.defaultGetStrategy,
          maxAge: CacheConstants.sessionListExpiryMs,
        );
        
        if (cached == null) {
          LogUtil.debug('缓存 $cacheKey 不存在或已过期，跳过');
          continue;
        }
        
        try {
          // 解析当前的分页数据
          final page = cached['page'] as int? ?? 1;
          final size = cached['size'] as int? ?? 10;
          final total = cached['total'] as int? ?? 0;
          
          // 解析Session列表
          final itemsJson = cached['items'] as List<dynamic>? ?? [];
          final items = itemsJson
            .map((item) => item is Map<String, dynamic> ? Session.fromMap(item) : null)
            .where((item) => item != null)
            .cast<Session>()
            .toList();
          
          // 从列表中移除被删除的会话
          final updatedItems = items.where((session) => session.id != deletedSessionId).toList();
          
          // 更新计数
          final updatedTotal = total > 0 ? total - 1 : 0;
          final updatedTotalPages = updatedTotal > 0 ? (updatedTotal / size).ceil() : 1;
          
          // 创建更新后的缓存数据
          final updatedCache = {
            'page': page,
            'size': size,
            'total': updatedTotal,
            'totalPages': updatedTotalPages,
            'hasNext': page < updatedTotalPages,
            'hasPrevious': page > 1,
            'items': updatedItems.map((session) => session.toMap()).toList(),
          };
          
          // 生成标准化的缓存键
          final Map<String, dynamic> params = {
            'page': page, 
            'size': size
          };
          final standardKey = _generateCacheKey(_sessionListCacheKey, params);
          
          // 缓存优化：只保留一个特定页码的缓存版本
          consolidatedCaches[page] = updatedCache;
          oldToNewKeyMap[cacheKey] = standardKey;
          
          LogUtil.debug('处理缓存: $cacheKey -> $standardKey (页码: $page)');
        } catch (e) {
          LogUtil.error('处理会话列表缓存 $cacheKey 失败: $e');
          
          // 如果处理失败，删除这个特定的缓存条目
          await _cacheManager.remove(cacheKey, strategy: CacheConstants.defaultCacheStrategy);
          LogUtil.debug('已删除无法处理的会话列表缓存: $cacheKey');
        }
      }
      
      // 更新缓存：使用标准化的缓存键
      int updatedCount = 0;
      for (final entry in consolidatedCaches.entries) {
        final page = entry.key;
        final updatedCache = entry.value;
        
        // 生成标准化的缓存键
        final Map<String, dynamic> params = {
          'page': page, 
          'size': updatedCache['size'] as int
        };
        final standardKey = _generateCacheKey(_sessionListCacheKey, params);
        
        // 更新缓存，使用新的标准化键
        await _cacheManager.set(
          standardKey,
          updatedCache,
          strategy: CacheConstants.defaultCacheStrategy,
          expiry: CacheConstants.sessionListExpiryMs,
        );
        
        updatedCount++;
      }
      
      // 清除所有旧的缓存键
      for (final oldKey in oldToNewKeyMap.keys) {
        final newKey = oldToNewKeyMap[oldKey];
        if (newKey != oldKey) {
          await _cacheManager.remove(oldKey, strategy: CacheConstants.defaultCacheStrategy);
          LogUtil.debug('删除重复缓存: $oldKey，使用标准键: $newKey');
        }
      }
      
      LogUtil.debug('会话列表缓存增量更新完成，从 ${sessionListCacheKeys.length} 个缓存优化到 $updatedCount 个');
    } catch (e) {
      LogUtil.error('更新会话列表缓存失败: $e');
      
      // 如果整个增量更新过程失败，回退到清空所有列表缓存
      LogUtil.warn('增量更新失败，回退到清空所有会话列表缓存');
      await _clearListCache();
    }
  }
  
  /// 清除缓存
  Future<void> clearCache() async {
    try {
      LogUtil.debug('清除会话缓存');
      
      // 清除所有会话相关缓存
      await _clearListCache();
      
      LogUtil.debug('已清除所有会话缓存');
    } catch (e) {
      LogUtil.error('清除会话缓存失败: $e');
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: false);
    }
  }
}